from google.adk.sessions import DatabaseSessionService
from sqlalchemy import delete
from google.adk.sessions.database_session_service import StorageEvent
from typing import List
import logging

logger = logging.getLogger(__name__)


class CustomSessionService(DatabaseSessionService):
    def __init__(self, db_url: str):
        super().__init__(db_url)
        # Any additional initialization specific to CustomSessionService goes here

    async def delete_events_by_ids(
        self,
        event_ids: List[str],
        app_name: str,
        user_id: str,
        session_id: str,
    ) -> int:
        """
        Deletes events from the database based on a list of event IDs
        for a specific app, user, and session.

        Args:
            event_ids: A list of event ID strings to delete.
            app_name: The application name.
            user_id: The user ID.
            session_id: The session ID to which these events belong.

        Returns:
            The number of events deleted.
        """
        if not event_ids:
            logger.info("No event IDs provided for deletion.")
            return 0

        logger.info(
            f"Attempting to delete events with IDs: {event_ids} for app '{self}', "
            f"user '{user_id}', session '{session_id}'"
        )

        deleted_count = 0
        with self.DatabaseSessionFactory() as db_session:
            try:
                # The StorageEvent has a composite primary key including app_name, user_id, session_id, and id.
                # We need to filter by all these to correctly target the events.
                stmt = (
                    delete(StorageEvent)
                    .where(StorageEvent.id.in_(event_ids))
                    .where(StorageEvent.app_name == app_name)
                    .where(StorageEvent.user_id == user_id)
                    .where(StorageEvent.session_id == session_id)
                )
                result = db_session.execute(stmt)
                db_session.commit()
                deleted_count = result.rowcount
                logger.info(f"Successfully deleted {deleted_count} events.")

                # # Update session timestamp with the latest remaining event
                # if deleted_count > 0:
                #     # Query for the latest timestamp among remaining events
                #     latest_event_result = (
                #         db_session.query(StorageEvent.timestamp)
                #         .filter(StorageEvent.app_name == app_name)
                #         .filter(StorageEvent.user_id == user_id)
                #         .filter(StorageEvent.session_id == session_id)
                #         .order_by(StorageEvent.timestamp.desc())
                #         .first()
                #     )

                #     if latest_event_result:
                #         latest_timestamp = latest_event_result[0]
                #         # Update the session's last_updated timestamp
                #         db_session.query(StorageSession).filter(
                #             StorageSession.app_name == app_name,
                #             StorageSession.user_id == user_id,
                #             StorageSession.id == session_id,
                #         ).update({"last_updated": latest_timestamp})
                #         logger.info(f"Updated session timestamp to {latest_timestamp}")
                #     else:
                #         # No events remain, update to current time
                #         import time

                #         current_time = time.time()
                #         db_session.query(StorageSession).filter(
                #             StorageSession.app_name == app_name,
                #             StorageSession.user_id == user_id,
                #             StorageSession.id == session_id,
                #         ).update({"last_updated": current_time})
                #         logger.info(
                #             f"No events remain, updated session timestamp to current time: {current_time}"
                #         )

                # db_session.commit()
            except Exception as e:
                db_session.rollback()
                logger.error(f"Error deleting events: {e}", exc_info=True)
                raise  # Re-raise the exception to allow higher-level handling
            finally:
                # The session is automatically closed by the 'with' statement
                pass

        return deleted_count

    # --- END OF NEW METHOD ---(self, session, event):


db_url = "sqlite:///./data/session.db"
# Choose the appropriate session service based on the environment
# SessionService = (
#     InMemorySessionService() if is_development_mode() else CustomSessionService(db_url)
# )
SessionService = CustomSessionService(db_url)
