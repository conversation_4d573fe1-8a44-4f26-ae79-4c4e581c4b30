"""
Utility functions for processing events from agent responses and session data.
"""

from typing import Dict, Any
import logging
from google.genai.types import Part
from google.adk.events import Event

logger = logging.getLogger(__name__)


def process_agent_events(event: Event):
    """Process a single event from an agent's response."""
    # Default payload with safe fallbacks
    payload = {
        "role": "",
        "content": "",
        "is_final_response": False,
        "event": "message",
    }

    try:
        # Set role from content
        payload["role"] = event.content.role

        # Get the part
        part: Part = event.content.parts[0]

        # Partial message
        if event.partial:
            payload["event"] = "partial_message"
            payload["content"] = part.text
            return payload

        # Function call
        if part.function_call:
            payload["event"] = "function_call"
            payload["content"] = {
                "function_call": {
                    "id": part.function_call.id,
                    "name": part.function_call.name,
                    "arguments": part.function_call.args,
                    "title": part.function_call.args.get("title", ""),
                    "description": part.function_call.args.get("description", ""),
                }
            }
            return payload

        # Function response
        if part.function_response:
            payload["event"] = "function_response"
            payload["role"] = "model"

            # Get response data
            response = part.function_response.response

            # Create response object
            response_data = {
                "status": "",
                "content": response,
                "artifact_id": "",
                "artifact_type": "",
            }

            # If response is a dict, extract fields
            if isinstance(response, dict):
                response_data["status"] = response.get("status", "")
                response_data["content"] = response.get("content", "")
                response_data["artifact_id"] = response.get("artifact_id", "")
                response_data["artifact_type"] = response.get("artifact_type", "")

            # Create function response object
            function_response = {
                "id": part.function_response.id,
                "name": part.function_response.name,
                "response": response_data,
            }

            # Add actions if present
            if (
                hasattr(part.function_response, "actions")
                and part.function_response.actions
            ):
                function_response["actions"] = part.function_response.actions

            payload["content"] = {"function_response": function_response}
            return payload

        # Regular text message
        payload["content"] = part.text

        # Set final response flag
        if hasattr(event, "is_final_response") and callable(event.is_final_response):
            payload["is_final_response"] = event.is_final_response()

        return payload

    except AttributeError as e:
        # Handle missing attributes gracefully
        logger.error(f"Missing attribute in event: {str(e)}")
        return payload

    except Exception as e:
        # Handle other errors
        logger.error(f"Error processing agent event: {str(e)}")
        return {
            "event": "error",
            "content": str(e),
            "role": "model",
            "type": "service_error",
        }


def process_session_events(session_data) -> Dict[str, Any]:
    """Process a session and its events."""
    try:
        # Create session object with basic info
        processed_session = {
            "id": session_data.id,
            "app_name": session_data.app_name,
            "user_id": session_data.user_id,
            "state": session_data.state,
            "events": [],
        }

        # Return early if no events
        if not session_data.events:
            return processed_session

        # Process each event
        for event in session_data.events:
            if event.invocation_id == "greeting_update" or event.invocation_id == "initial_query_update":
                continue
            # Process this event using our existing function
            processed_event = process_agent_events(event)

            # Add to events list
            processed_session["events"].append(processed_event)

        return processed_session

    except Exception as e:
        logger.error(f"Error processing session events: {str(e)}")
        # Return a minimal valid response with error info
        return {
            "id": getattr(session_data, "id", "unknown"),
            "error": str(e),
            "events": [],
        }
