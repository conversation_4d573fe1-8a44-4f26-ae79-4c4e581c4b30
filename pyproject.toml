[project]
name = "agents-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "chromadb>=1.0.7",
    "duckdb>=1.2.2",
    "google-adk>=0.3.0",
    "langchain-huggingface>=0.1.2",
    "litellm>=1.67.5",
    "numpy>=2.2.5",
    "pandas>=2.2.3",
    "sentence-transformers>=4.1.0",
    "uvicorn>=0.34.2",
    "tabulate==0.9.0",
    "plotly>=6.0.1",
    "fastapi[standard]>=0.115.9",
]

[dependency-groups]
dev = [
    "ruff>=0.11.9",
]
[tool.ruff.format]
docstring-code-format = true
docstring-code-line-length = 20
