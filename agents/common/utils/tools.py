from google.adk.tools import LongRunningFunctionTool
import asyncio
from typing import Any, Dict
from fastapi import FastAPI, Request
from pydantic import BaseModel
from google.adk.tools import ToolContext
import logging

logger = logging.getLogger(__name__)



async def actions(function_name: str, payload:dict,title: str, description: str,tool_context:ToolContext) -> dict:
    """Tool to fetch/update the user interface of the application which the user is using.
    
    Args:
        function_name (str): name of the function from the user interface.
        payload (dict): data to be passed to the function.
        title (str): Title for the artifact to display.
        description (str): Description of the query's goal in 25 words.
        
    Returns:
        dict: name of the  data returned from the function to be used by the agent.
    """
    return {'name': function_name, 'title':title,'description':description,'status':'pending'}




# Wrap the function
actions_tool = LongRunningFunctionTool(func=actions)