"""
Action names organized by module for validation and management.
"""

# Module 1 actions
MODULE_1_ACTIONS = [
    # Add action names for module 1 here
    # Example: "create_user", "update_user", "delete_user"
]

# Module 2 actions
MODULE_2_ACTIONS = [
    # Add action names for module 2 here
    # Example: "send_email", "send_notification"
]

# Module 3 actions
MODULE_3_ACTIONS = [
    # Add action names for module 3 here
    # Example: "generate_report", "export_data"
]

# Combined list of all action names from all modules
ALL_ACTIONS = MODULE_1_ACTIONS + MODULE_2_ACTIONS + MODULE_3_ACTIONS


def is_valid_action(action_name: str) -> bool:
    """
    Check if the given action name is valid (exists in any module).

    Args:
        action_name: The name of the action to validate

    Returns:
        bool: True if action is valid, False otherwise
    """
    return action_name in ALL_ACTIONS


def get_all_actions() -> list[str]:
    """
    Get all available action names.

    Returns:
        list[str]: List of all action names from all modules
    """
    return ALL_ACTIONS.copy()


def get_actions_by_module() -> dict[str, list[str]]:
    """
    Get actions organized by module.

    Returns:
        dict: Dictionary with module names as keys and action lists as values
    """
    return {
        "module_1": MODULE_1_ACTIONS.copy(),
        "module_2": MODULE_2_ACTIONS.copy(),
        "module_3": MODULE_3_ACTIONS.copy(),
    }
