"""
Action names organized by module for validation and management.
"""

# Terra module actions
TERRA_ACTIONS = [
    # Add terra module action names here
    # Example: "analyze_terrain", "process_satellite_data"
]

# PM (Project Management) module actions
PM_ACTIONS = [
    # Add PM module action names here
    # Example: "create_project", "update_milestone", "assign_task"
]

# Forms module actions
FORMS_ACTIONS = [
    # Add forms module action names here
    # Example: "create_form", "submit_form", "validate_form"
]

# Combined list of all action names from all modules
ALL_ACTIONS = TERRA_ACTIONS + PM_ACTIONS + FORMS_ACTIONS


def is_valid_action(action_name: str) -> bool:
    """
    Check if the given action name is valid (exists in any module).

    Args:
        action_name: The name of the action to validate

    Returns:
        bool: True if action is valid, False otherwise
    """
    return action_name in ALL_ACTIONS


def get_all_actions() -> list[str]:
    """
    Get all available action names.

    Returns:
        list[str]: List of all action names from all modules
    """
    return ALL_ACTIONS.copy()


def get_actions_by_module() -> dict[str, list[str]]:
    """
    Get actions organized by module.

    Returns:
        dict: Dictionary with module names as keys and action lists as values
    """
    return {
        "terra": TERRA_ACTIONS.copy(),
        "pm": PM_ACTIONS.copy(),
        "forms": FORMS_ACTIONS.copy(),
    }
