import sys
import os
from dotenv import load_dotenv

# Add the agents directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../agents-backend'))

load_dotenv(".env", override=True)
load_dotenv(".env.local", override=True)


from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from .utils.tools import think, activity_finder, sql, display_chart
from .utils.prompts import get_prompt


def get_agent(metadata={}):

    model = metadata.get("model", "anthropic/claude-3-5-sonnet-latest")

    config = {
        "name": "pm_agent",
        "model": LiteLlm(model=model, parallel_tool_calls=False, drop_params=True),
        "instruction": get_prompt({}),
        "tools": [think, activity_finder, sql, display_chart]
    }

    return Agent(**config)


root_agent = get_agent({})
